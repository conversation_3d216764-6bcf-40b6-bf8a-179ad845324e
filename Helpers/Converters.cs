using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Helpers
{
    public class InverseBoolConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue) return !boolValue;
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class NotNullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value != null ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ApartmentStatus status)
            {
                return status switch
                {
                    ApartmentStatus.Available => new SolidColorBrush(Colors.Green),
                    ApartmentStatus.Occupied => new SolidColorBrush(Colors.Blue),
                    ApartmentStatus.UnderMaintenance => new SolidColorBrush(Colors.Orange),
                    ApartmentStatus.Reserved => new SolidColorBrush(Colors.Purple),
                    _ => new SolidColorBrush(Colors.Gray)
                };
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class PaymentStatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PaymentStatus status)
            {
                return status switch
                {
                    PaymentStatus.Paid => new SolidColorBrush(Colors.Green),
                    PaymentStatus.Pending => new SolidColorBrush(Colors.Orange),
                    PaymentStatus.Overdue => new SolidColorBrush(Colors.Red),
                    PaymentStatus.Cancelled => new SolidColorBrush(Colors.Gray),
                    _ => new SolidColorBrush(Colors.Gray)
                };
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 