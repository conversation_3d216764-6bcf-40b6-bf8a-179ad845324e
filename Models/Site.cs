using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    public class Site
    {
        public string Id { get; set; } = string.Empty;

        [Required(ErrorMessage = "Site adı zorunludur")]
        [StringLength(100, ErrorMessage = "Site adı en fazla 100 karakter olabilir")]
        public string SiteName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Adres zorunludur")]
        [StringLength(500, ErrorMessage = "Adres en fazla 500 karakter olabilir")]
        public string Address { get; set; } = string.Empty;

        public string Phone { get; set; } = string.Empty;

        public string OwnerId { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // Navigation property - Firestore'da ayrı collection olarak tutulacak
        public List<Apartment> Apartments { get; set; } = new List<Apartment>();

        public string Status => IsActive ? "Aktif" : "Pasif";

        public Site Clone()
        {
            return (Site)this.MemberwiseClone();
        }
    }
}
