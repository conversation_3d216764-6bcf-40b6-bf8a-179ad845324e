using System;

namespace ApartmanYonetimSistemi.Models
{
    public class Payment
    {
        public string Id { get; set; } = string.Empty;
        public string ApartmentId { get; set; } = string.Empty;
        public Apartment? Apartment { get; set; }
        public string ResidentId { get; set; } = string.Empty;
        public User? Resident { get; set; }
        public decimal Amount { get; set; }
        public string Type { get; set; } = string.Empty;
        public PaymentStatus Status { get; set; }
        public DateTime DueDate { get; set; }
        public DateTime? PaidDate { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }

        // Additional properties for compatibility with ViewModels
        public string FlatId { get; set; } = string.Empty;
        public string TenantId { get; set; } = string.Empty;
        public string SiteId { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;

        // Computed properties for UI
        public string FlatNo { get; set; } = string.Empty;
        public string TenantName { get; set; } = string.Empty;
    }

    public enum PaymentType
    {
        Rent,
        Maintenance,
        Electricity,
        Water,
        Gas,
        Other
    }

    public enum PaymentStatus
    {
        Pending,
        Paid,
        Overdue,
        Cancelled
    }
} 