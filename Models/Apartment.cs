using System;
using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    public class Apartment
    {
        public string Id { get; set; } = string.Empty;

        [Required(ErrorMessage = "Apartman adı zorunludur")]
        [StringLength(100, ErrorMessage = "Apartman adı en fazla 100 karakter olabilir")]
        public string ApartmentName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Site ID zorunludur")]
        public string SiteId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Adres zorunludur")]
        [StringLength(500, ErrorMessage = "Adres en fazla 500 karakter olabilir")]
        public string Address { get; set; } = string.Empty;

        [Range(1, 1000, ErrorMessage = "Toplam daire sayısı 1-1000 arasında olmalıdır")]
        public int TotalFlats { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        // Legacy properties for backward compatibility
        public string Number { get; set; } = string.Empty;
        public int Floor { get; set; }
        public int RoomCount { get; set; }
        public decimal Area { get; set; }
        public ApartmentStatus Status { get; set; }
        public int? ResidentId { get; set; }
        public User? Resident { get; set; }
        public decimal MonthlyRent { get; set; }
        public DateTime CreatedAt { get; set; }

        public Apartment Clone()
        {
            return (Apartment)this.MemberwiseClone();
        }
    }

    public enum ApartmentStatus
    {
        Available,
        Occupied,
        UnderMaintenance,
        Reserved
    }
}