using System;

namespace ApartmanYonetimSistemi.Models
{
    public class User
    {
        public string Id { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public UserRoles Role { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public enum UserRoles
    {
        Ad<PERSON>,
        Manager,
        Resident,
        Viewer
    }
} 