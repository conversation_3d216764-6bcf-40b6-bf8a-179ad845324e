using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    public class Tenant
    {
        public string Id { get; set; } = string.Empty;

        [Required(ErrorMessage = "Ad zorunludur")]
        [StringLength(50, ErrorMessage = "Ad en fazla 50 karakter olabilir")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Soyad zorunludur")]
        [StringLength(50, ErrorMessage = "Soyad en fazla 50 karakter olabilir")]
        public string Surname { get; set; } = string.Empty;

        [Required(ErrorMessage = "Telefon numarası zorunludur")]
        public string Phone { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email zorunludur")]
        [EmailAddress(ErrorMessage = "Geçerli bir email adresi giriniz")]
        public string Email { get; set; } = string.Empty;

        public string FlatId { get; set; } = string.Empty;

        public string SiteId { get; set; } = string.Empty;

        public string ApartmentId { get; set; } = string.Empty;

        public DateTime EntryDate { get; set; } = DateTime.Now;

        public DateTime? ExitDate { get; set; }

        [StringLength(11, MinimumLength = 11, ErrorMessage = "TC Kimlik numarası 11 haneli olmalıdır")]
        public string IdentityNumber { get; set; } = string.Empty;

        public string EmergencyContact { get; set; } = string.Empty;

        public string EmergencyPhone { get; set; } = string.Empty;

        [StringLength(1000, ErrorMessage = "Notlar en fazla 1000 karakter olabilir")]
        public string Notes { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Computed properties
        public string FullName => $"{Name} {Surname}";
        public string FlatNo { get; set; } = string.Empty; // For UI display

        public Tenant Clone()
        {
            return (Tenant)this.MemberwiseClone();
        }
    }
}
