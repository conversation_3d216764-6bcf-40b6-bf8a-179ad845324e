using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    public class Flat
    {
        public string Id { get; set; } = string.Empty;

        [Required(ErrorMessage = "Daire numarası zorunludur")]
        [StringLength(10, ErrorMessage = "Daire numarası en fazla 10 karakter olabilir")]
        public string FlatNo { get; set; } = string.Empty;

        [Required(ErrorMessage = "Apartman ID zorunludur")]
        public string ApartmentId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Site ID zorunludur")]
        public string SiteId { get; set; } = string.Empty;

        [Range(0, double.MaxValue, ErrorMessage = "Kira bedeli 0'dan büyük olmalıdır")]
        public decimal RentAmount { get; set; }

        public string RentStatus { get; set; } = "Due"; // Paid/Due

        [Range(0, double.MaxValue, ErrorMessage = "Aidat 0'dan büy<PERSON>k olmalıdır")]
        public decimal Dues { get; set; }

        public string DuesStatus { get; set; } = "Due"; // Paid/Due

        public DateTime RentDueDate { get; set; } = DateTime.Now.AddMonths(1);

        public DateTime DuesDueDate { get; set; } = DateTime.Now.AddMonths(1);

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Additional properties for UI
        public int Floor { get; set; }
        public int RoomCount { get; set; }
        public decimal Area { get; set; }
        public string Status { get; set; } = "Boş";
        public decimal MonthlyRent { get; set; }

        public Flat Clone()
        {
            return (Flat)this.MemberwiseClone();
        }
    }
}
