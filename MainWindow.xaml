<Window x:Class="ApartmanYonetimSistemi.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ApartmanYonetimSistemi"
        xmlns:views="clr-namespace:ApartmanYonetimSistemi.Views"
        mc:Ignorable="d"
        Title="Apartman Yönetim Sistemi" 
        Height="720" Width="1280"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
        ResizeMode="CanResize">

    <Window.Resources>
        <Style x:Key="WindowControlButton" TargetType="Button">
            <Setter Property="Width" Value="35"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#888"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="50">
                            <ContentPresenter VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E5E5E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="WindowCloseButton" TargetType="Button" BasedOn="{StaticResource WindowControlButton}">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E81123"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <!-- Ana Pencere Kenarlığı -->
    <Border Background="#F7F8FA" CornerRadius="15" BorderBrush="#E0E0E0" BorderThickness="1">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="450" />
            </Grid.ColumnDefinitions>

            <!-- Sol Taraf - Görsel Alanı -->
            <Border Grid.Column="0" CornerRadius="15,0,0,15" Background="#6C63FF">
                <Grid>
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="40">
                        <TextBlock Text="🏢" FontSize="80" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                        <TextBlock Text="Apartman Yönetim Sistemi" 
                                   FontSize="32" 
                                   FontWeight="Bold" 
                                   Foreground="White" 
                                   HorizontalAlignment="Center" 
                                   TextWrapping="Wrap" 
                                   TextAlignment="Center" 
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="Modern ve kullanıcı dostu apartman yönetim çözümü" 
                                   FontSize="16" 
                                   Foreground="#E0E0E0" 
                                   HorizontalAlignment="Center" 
                                   TextWrapping="Wrap" 
                                   TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Sağ Taraf - Login Alanı -->
            <Grid Grid.Column="1" Background="White">
                <views:LoginView/>
            </Grid>

            <!-- Pencere Kontrol Butonları -->
            <StackPanel Grid.Column="1" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right" 
                        VerticalAlignment="Top" 
                        Margin="10">
                <Button Content="—" 
                        Click="MinimizeButton_Click" 
                        Style="{StaticResource WindowControlButton}"/>
                <Button Content="☐" 
                        Click="MaximizeButton_Click" 
                        Style="{StaticResource WindowControlButton}"/>
                <Button Content="✕" 
                        Click="CloseButton_Click" 
                        Style="{StaticResource WindowCloseButton}"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
