<UserControl x:Class="ApartmanYonetimSistemi.Views.LoginView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ApartmanYonetimSistemi.Views"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:helpers="clr-namespace:ApartmanYonetimSistemi.Helpers"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="450">

    <UserControl.DataContext>
        <vm:LoginViewModel/>
    </UserControl.DataContext>

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <helpers:InverseBoolConverter x:Key="InverseBoolConverter"/>
        <helpers:NotNullToVisibilityConverter x:Key="NotNullToVisibilityConverter"/>
    </UserControl.Resources>
    
    <Grid>
        <Border Padding="40,0" VerticalAlignment="Center" HorizontalAlignment="Center">
            <StackPanel Width="350">

                <!-- Başlık Alanı -->
                <StackPanel Margin="0,0,0,30">
                    <materialDesign:PackIcon Kind="HomeCity" 
                                             Width="50" 
                                             Height="50" 
                                             HorizontalAlignment="Center" 
                                             Foreground="#6C63FF" 
                                             Margin="0,0,0,15"/>
                    <TextBlock Text="Hoş Geldiniz" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center" 
                               Margin="0,0,0,5" 
                               TextWrapping="Wrap" 
                               TextAlignment="Center"/>
                    <TextBlock Text="Devam etmek için giriş yapın" 
                               FontSize="16" 
                               Foreground="Gray" 
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Hata Mesajı Alanı -->
                <materialDesign:Card x:Name="ErrorCard" 
                                     Padding="15" 
                                     Background="#F44336" 
                                     Margin="0,0,0,15"
                                     Visibility="{Binding ErrorMessage, Converter={StaticResource NotNullToVisibilityConverter}}">
                    <TextBlock Text="{Binding ErrorMessage}" 
                               TextWrapping="Wrap" 
                               Foreground="White"/>
                </materialDesign:Card>

                <!-- E-posta Alanı -->
                <TextBox Margin="0,0,0,20"
                         materialDesign:HintAssist.Hint="E-posta Adresiniz"
                         Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         FontSize="14"/>

                <!-- Şifre Alanı -->
                <PasswordBox Margin="0,0,0,30" 
                             x:Name="PasswordBox"
                             materialDesign:HintAssist.Hint="Şifreniz"
                             Style="{StaticResource MaterialDesignFloatingHintPasswordBox}"
                             FontSize="14"/>

                <!-- Giriş Butonu -->
                <Button Content="GİRİŞ YAP" 
                        Height="50"
                        Command="{Binding LoginCommand}"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBoolConverter}}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="#6C63FF"/>

                <!-- Yükleniyor İndikatörü -->
                <ProgressBar IsIndeterminate="True" 
                             Margin="0,20,0,0"
                             Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}"
                             Style="{StaticResource MaterialDesignCircularProgressBar}"/>

                <!-- Şifremi Unuttum ve Kayıt Ol Linkleri -->
                <Grid Margin="0,25,0,0">
                    <TextBlock HorizontalAlignment="Left">
                        <Hyperlink Foreground="Gray" 
                                   TextDecorations="None" 
                                   Command="{Binding ShowForgotPasswordWindowCommand}">Şifremi Unuttum</Hyperlink>
                    </TextBlock>
                    <TextBlock HorizontalAlignment="Right">
                        <Run Text="Hesabın yok mu?" Foreground="Gray"/>
                        <Hyperlink FontWeight="Bold" 
                                   Command="{Binding ShowRegisterWindowCommand}">Kayıt Ol</Hyperlink>
                    </TextBlock>
                </Grid>

            </StackPanel>
        </Border>
    </Grid>
</UserControl> 