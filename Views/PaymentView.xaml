<UserControl x:Class="ApartmanYonetimSistemi.Views.PaymentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:PaymentViewModel/>
    </UserControl.DataContext>

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <TextBlock Grid.Row="0" 
                   Text="Ödeme Yönetimi" 
                   FontSize="28" FontWeight="Bold" 
                   Margin="0,0,0,20"/>

        <!-- Ödeme Listesi -->
        <materialDesign:Card Grid.Row="1" materialDesign:ElevationAssist.Elevation="Dp4">
            <DataGrid ItemsSource="{Binding Payments}"
                      SelectedItem="{Binding SelectedPayment, Mode=TwoWay}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Daire No" 
                                        Binding="{Binding FlatNo}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Kiracı" 
                                        Binding="{Binding TenantName}" 
                                        Width="*"/>
                    <DataGridTextColumn Header="Tutar" 
                                        Binding="{Binding Amount, StringFormat=C}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Tür" 
                                        Binding="{Binding Type}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Vade Tarihi" 
                                        Binding="{Binding DueDate, StringFormat=dd.MM.yyyy}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Durum" 
                                        Binding="{Binding Status}" 
                                        Width="Auto"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
