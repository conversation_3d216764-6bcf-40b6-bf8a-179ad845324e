<UserControl x:Class="ApartmanYonetimSistemi.Views.ApartmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:ApartmentViewModel/>
    </UserControl.DataContext>

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <TextBlock Grid.Row="0" 
                   Text="Apartman Yönetimi" 
                   FontSize="28" FontWeight="Bold" 
                   Margin="0,0,0,20"/>

        <!-- Araç Çubuğu -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Command="{Binding NewApartmentCommand}"
                    Style="{DynamicResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Margin="0,0,10,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Yeni Apartman"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding EditApartmentCommand}"
                    Style="{DynamicResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Edit"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Düzenle"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding DeleteApartmentCommand}"
                    Style="{DynamicResource MaterialDesignOutlinedButton}"
                    Foreground="Red"
                    BorderBrush="Red">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Delete"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Sil"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding LoadApartmentsCommand}"
                    Style="{DynamicResource MaterialDesignFlatButton}"
                    Margin="20,0,0,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Yenile"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>

        <!-- Apartman Listesi -->
        <materialDesign:Card Grid.Row="2" materialDesign:ElevationAssist.Elevation="Dp4">
            <DataGrid ItemsSource="{Binding Apartments}"
                      SelectedItem="{Binding SelectedApartment, Mode=TwoWay}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Apartman Adı" 
                                        Binding="{Binding ApartmentName}" 
                                        Width="*"/>
                    <DataGridTextColumn Header="Adres" 
                                        Binding="{Binding Address}" 
                                        Width="2*"/>
                    <DataGridTextColumn Header="Toplam Daire" 
                                        Binding="{Binding TotalFlats}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Oluşturma Tarihi" 
                                        Binding="{Binding CreatedDate, StringFormat=dd.MM.yyyy}" 
                                        Width="Auto"/>
                    <DataGridCheckBoxColumn Header="Aktif" 
                                            Binding="{Binding IsActive}" 
                                            Width="Auto"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>

        <!-- Apartman Detay/Düzenleme Paneli -->
        <materialDesign:Card Grid.Row="3" 
                           Margin="0,20,0,0"
                           Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                           materialDesign:ElevationAssist.Elevation="Dp4">
            <StackPanel Margin="20">
                <TextBlock Text="Apartman Bilgileri" 
                           FontSize="20" FontWeight="Bold" 
                           Margin="0,0,0,20"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Apartman Adı -->
                    <TextBox Grid.Row="0" Grid.Column="0"
                             materialDesign:HintAssist.Hint="Apartman Adı"
                             Text="{Binding CurrentApartment.ApartmentName, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="0,0,10,20"/>

                    <!-- Toplam Daire -->
                    <TextBox Grid.Row="0" Grid.Column="1"
                             materialDesign:HintAssist.Hint="Toplam Daire Sayısı"
                             Text="{Binding CurrentApartment.TotalFlats, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="10,0,0,20"/>

                    <!-- Adres -->
                    <TextBox Grid.Row="1" Grid.ColumnSpan="2"
                             materialDesign:HintAssist.Hint="Adres"
                             Text="{Binding CurrentApartment.Address, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="0,0,0,20"/>

                    <!-- Aktif Durumu -->
                    <CheckBox Grid.Row="2" Grid.ColumnSpan="2"
                              Content="Aktif"
                              IsChecked="{Binding CurrentApartment.IsActive}"
                              Style="{DynamicResource MaterialDesignCheckBox}"
                              Margin="0,0,0,20"/>

                    <!-- Butonlar -->
                    <StackPanel Grid.Row="3" Grid.ColumnSpan="2" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Content="İptal"
                                Command="{Binding CancelEditCommand}"
                                Style="{DynamicResource MaterialDesignFlatButton}"
                                Margin="0,0,10,0"/>
                        <Button Content="Kaydet"
                                Command="{Binding SaveApartmentCommand}"
                                Style="{DynamicResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- Mesaj Alanları -->
        <StackPanel Grid.Row="3" Margin="0,10,0,0">
            <TextBlock Text="{Binding ErrorMessage}"
                       Foreground="Red"
                       Visibility="{Binding ErrorMessage, Converter={StaticResource BoolToVisibilityConverter}}"
                       Margin="0,5"/>
            <TextBlock Text="{Binding SuccessMessage}"
                       Foreground="Green"
                       Visibility="{Binding SuccessMessage, Converter={StaticResource BoolToVisibilityConverter}}"
                       Margin="0,5"/>
        </StackPanel>
    </Grid>
</UserControl>
