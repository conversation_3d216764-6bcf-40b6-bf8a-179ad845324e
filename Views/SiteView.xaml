<UserControl x:Class="ApartmanYonetimSistemi.Views.SiteView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:SiteViewModel/>
    </UserControl.DataContext>

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <TextBlock Grid.Row="0" 
                   Text="Site Yönetimi" 
                   FontSize="28" FontWeight="Bold" 
                   Margin="0,0,0,20"/>

        <!-- Araç Çubuğu -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Command="{Binding NewSiteCommand}"
                    Style="{DynamicResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Margin="0,0,10,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Yeni Site"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding EditSiteCommand}"
                    Style="{DynamicResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Edit"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Düzenle"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding DeleteSiteCommand}"
                    Style="{DynamicResource MaterialDesignOutlinedButton}"
                    Foreground="Red"
                    BorderBrush="Red">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Delete"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Sil"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding LoadSitesCommand}"
                    Style="{DynamicResource MaterialDesignFlatButton}"
                    Margin="20,0,0,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh"
                                               VerticalAlignment="Center"
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Yenile"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>

        <!-- Site Listesi -->
        <materialDesign:Card Grid.Row="2" materialDesign:ElevationAssist.Elevation="Dp4">
            <DataGrid ItemsSource="{Binding Sites}"
                      SelectedItem="{Binding SelectedSite, Mode=TwoWay}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Site Adı" 
                                        Binding="{Binding SiteName}" 
                                        Width="*"/>
                    <DataGridTextColumn Header="Adres" 
                                        Binding="{Binding Address}" 
                                        Width="2*"/>
                    <DataGridTextColumn Header="Apartman Sayısı" 
                                        Binding="{Binding TotalApartments}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Oluşturma Tarihi" 
                                        Binding="{Binding CreatedDate, StringFormat=dd.MM.yyyy}" 
                                        Width="Auto"/>
                    <DataGridCheckBoxColumn Header="Aktif" 
                                            Binding="{Binding IsActive}" 
                                            Width="Auto"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>

        <!-- Site Detay/Düzenleme Paneli -->
        <materialDesign:Card Grid.Row="3" 
                           Margin="0,20,0,0"
                           Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                           materialDesign:ElevationAssist.Elevation="Dp4">
            <StackPanel Margin="20">
                <TextBlock Text="{Binding IsEditing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Site Düzenle|Yeni Site'}" 
                           FontSize="20" FontWeight="Bold" 
                           Margin="0,0,0,20"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Site Adı -->
                    <TextBox Grid.Row="0" Grid.Column="0"
                             materialDesign:HintAssist.Hint="Site Adı"
                             Text="{Binding CurrentSite.SiteName, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="0,0,10,20"/>

                    <!-- Telefon -->
                    <TextBox Grid.Row="0" Grid.Column="1"
                             materialDesign:HintAssist.Hint="Telefon"
                             Text="{Binding CurrentSite.Phone, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="10,0,0,20"/>

                    <!-- Adres -->
                    <TextBox Grid.Row="1" Grid.ColumnSpan="2"
                             materialDesign:HintAssist.Hint="Adres"
                             Text="{Binding CurrentSite.Address, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="0,0,0,20"/>

                    <!-- Butonlar -->
                    <StackPanel Grid.Row="2" Grid.ColumnSpan="2" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Content="İptal"
                                Command="{Binding CancelEditCommand}"
                                Style="{DynamicResource MaterialDesignFlatButton}"
                                Margin="0,0,10,0"/>
                        <Button Content="Kaydet"
                                Command="{Binding SaveSiteCommand}"
                                Style="{DynamicResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- Mesaj Alanları -->
        <StackPanel Grid.Row="3" Margin="0,10,0,0">
            <TextBlock Text="{Binding ErrorMessage}"
                       Foreground="Red"
                       Visibility="{Binding ErrorMessage, Converter={StaticResource BoolToVisibilityConverter}}"
                       Margin="0,5"/>
            <TextBlock Text="{Binding SuccessMessage}"
                       Foreground="Green"
                       Visibility="{Binding SuccessMessage, Converter={StaticResource BoolToVisibilityConverter}}"
                       Margin="0,5"/>
        </StackPanel>
    </Grid>
</UserControl>
