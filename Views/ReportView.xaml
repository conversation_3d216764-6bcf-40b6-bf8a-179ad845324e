<UserControl x:Class="ApartmanYonetimSistemi.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:ReportViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <TextBlock Grid.Row="0" 
                   Text="Raporlar" 
                   FontSize="28" FontWeight="Bold" 
                   Margin="0,0,0,20"/>

        <!-- Rapor İçeriği -->
        <materialDesign:Card Grid.Row="1" materialDesign:ElevationAssist.Elevation="Dp4">
            <StackPanel Margin="20">
                <TextBlock Text="Rapor modülü geliştirme aşamasında..." 
                           FontSize="16" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
