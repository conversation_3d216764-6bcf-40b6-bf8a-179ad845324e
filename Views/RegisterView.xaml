<Window x:Class="ApartmanYonetimSistemi.Views.RegisterView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ApartmanYonetimSistemi.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        xmlns:helpers="clr-namespace:ApartmanYonetimSistemi.Helpers"
        mc:Ignorable="d"
        Title="Yeni Kullanıcı Kaydı" 
        Height="600" 
        Width="450"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
        ResizeMode="NoResize">

    <Window.DataContext>
        <vm:RegisterViewModel/>
    </Window.DataContext>

    <Window.Resources>
        <helpers:NotNullToVisibilityConverter x:Key="NotNullToVisibilityConverter"/>
        <Style x:Key="WindowCloseButton" TargetType="Button">
            <Setter Property="Width" Value="35"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#888"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="50">
                            <ContentPresenter VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E81123"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="10" BorderBrush="#E0E0E0" BorderThickness="1">
        <Grid>
            <!-- Pencere Kontrol Butonları -->
            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Right" 
                        VerticalAlignment="Top" 
                        Margin="10">
                <Button Content="✕" 
                        Click="CloseButton_Click" 
                        Style="{StaticResource WindowCloseButton}"/>
            </StackPanel>

            <StackPanel Margin="40" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="AccountPlus" 
                                         Width="50" 
                                         Height="50" 
                                         HorizontalAlignment="Center" 
                                         Foreground="#6C63FF" 
                                         Margin="0,0,0,20"/>
                <TextBlock Text="Hesap Oluştur" 
                           FontSize="24" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center" 
                           Margin="0,0,0,20"/>

                <TextBlock Text="{Binding ErrorMessage}" 
                           Foreground="Red" 
                           HorizontalAlignment="Center" 
                           Margin="0,0,0,10" 
                           Visibility="{Binding ErrorMessage, Converter={StaticResource NotNullToVisibilityConverter}}"/>

                <TextBox Margin="0,0,0,15"
                         materialDesign:HintAssist.Hint="Ad Soyad"
                         Text="{Binding FullName, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>

                <TextBox Margin="0,0,0,15"
                         materialDesign:HintAssist.Hint="E-posta Adresi"
                         Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>

                <PasswordBox Margin="0,0,0,15"
                             materialDesign:HintAssist.Hint="Şifre"
                             helpers:PasswordHelper.BoundPassword="{Binding Password, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource MaterialDesignFloatingHintPasswordBox}"/>
                
                <PasswordBox Margin="0,0,0,25"
                             materialDesign:HintAssist.Hint="Şifre Tekrar"
                             helpers:PasswordHelper.BoundPassword="{Binding ConfirmPassword, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource MaterialDesignFloatingHintPasswordBox}"/>

                <Button Content="KAYIT OL" 
                        Height="50" 
                        Command="{Binding RegisterCommand}" 
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="#6C63FF"/>
            </StackPanel>
        </Grid>
    </Border>
</Window> 