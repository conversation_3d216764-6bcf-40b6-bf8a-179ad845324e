<Window x:Class="ApartmanYonetimSistemi.Views.ForgotPasswordView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ApartmanYonetimSistemi.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        mc:Ignorable="d"
        Title="Şifre Sıfırlama" Height="400" Width="450"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ResizeMode="NoResize">

    <Window.DataContext>
        <vm:ForgotPasswordViewModel/>
    </Window.DataContext>

    <Window.Resources>
        <Style x:Key="WindowCloseButton" TargetType="Button">
            <Setter Property="Width" Value="35"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#888"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="50">
                            <ContentPresenter VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E81123"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="10" BorderBrush="#E0E0E0" BorderThickness="1">
        <Grid>
            <!-- Pencere Kontrol Butonları -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="10">
                <Button Content="✕" Click="CloseButton_Click" Style="{StaticResource WindowCloseButton}"/>
            </StackPanel>

            <StackPanel Margin="40" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="LockReset" Width="50" Height="50" HorizontalAlignment="Center" Foreground="#6C63FF" Margin="0,0,0,20"/>
                <TextBlock Text="Şifreni Sıfırla" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="Kayıtlı e-posta adresinizi girerek şifre sıfırlama bağlantısı alabilirsiniz." FontSize="14" Foreground="Gray" HorizontalAlignment="Center" TextWrapping="Wrap" TextAlignment="Center" Margin="0,0,0,25"/>

                <TextBox Margin="0,0,0,25"
                         materialDesign:HintAssist.Hint="E-posta Adresi"
                         Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>

                <Button Content="SIFIRLAMA BAĞLANTISI GÖNDER" Height="50" Command="{Binding SendResetLinkCommand}" Style="{StaticResource MaterialDesignRaisedButton}" Background="#6C63FF"/>
            </StackPanel>
        </Grid>
    </Border>
</Window> 