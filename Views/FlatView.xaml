<UserControl x:Class="ApartmanYonetimSistemi.Views.FlatView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:FlatViewModel/>
    </UserControl.DataContext>

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Başlık -->
        <TextBlock Grid.Row="0" 
                   Text="Daire Yönetimi" 
                   FontSize="28" FontWeight="Bold" 
                   Margin="0,0,0,20"/>

        <!-- Araç Çubuğu -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Command="{Binding NewFlatCommand}"
                    Style="{DynamicResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Margin="0,0,10,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" 
                                               VerticalAlignment="Center" 
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Yeni Daire"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding EditFlatCommand}"
                    Style="{DynamicResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Edit" 
                                               VerticalAlignment="Center" 
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Düzenle"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding DeleteFlatCommand}"
                    Style="{DynamicResource MaterialDesignOutlinedButton}"
                    Foreground="Red"
                    BorderBrush="Red">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Delete" 
                                               VerticalAlignment="Center" 
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Sil"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding LoadFlatsCommand}"
                    Style="{DynamicResource MaterialDesignFlatButton}"
                    Margin="20,0,0,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" 
                                               VerticalAlignment="Center" 
                                               Margin="0,0,5,0"/>
                        <TextBlock Text="Yenile"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>

        <!-- Daire Listesi -->
        <materialDesign:Card Grid.Row="2" materialDesign:ElevationAssist.Elevation="Dp4">
            <DataGrid ItemsSource="{Binding Flats}"
                      SelectedItem="{Binding SelectedFlat, Mode=TwoWay}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Daire No" 
                                        Binding="{Binding FlatNo}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Kat" 
                                        Binding="{Binding Floor}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Oda Sayısı" 
                                        Binding="{Binding RoomCount}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Alan (m²)" 
                                        Binding="{Binding Area}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Kira" 
                                        Binding="{Binding MonthlyRent, StringFormat=C}" 
                                        Width="Auto"/>
                    <DataGridTextColumn Header="Durum" 
                                        Binding="{Binding Status}" 
                                        Width="Auto"/>
                    <DataGridCheckBoxColumn Header="Aktif" 
                                            Binding="{Binding IsActive}" 
                                            Width="Auto"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>

        <!-- Daire Detay/Düzenleme Paneli -->
        <materialDesign:Card Grid.Row="3" 
                           Margin="0,20,0,0"
                           Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}"
                           materialDesign:ElevationAssist.Elevation="Dp4">
            <StackPanel Margin="20">
                <TextBlock Text="Daire Bilgileri" 
                           FontSize="20" FontWeight="Bold" 
                           Margin="0,0,0,20"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Daire No -->
                    <TextBox Grid.Row="0" Grid.Column="0"
                             materialDesign:HintAssist.Hint="Daire No"
                             Text="{Binding CurrentFlat.FlatNo, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="0,0,10,20"/>

                    <!-- Kat -->
                    <TextBox Grid.Row="0" Grid.Column="1"
                             materialDesign:HintAssist.Hint="Kat"
                             Text="{Binding CurrentFlat.Floor, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="5,0,5,20"/>

                    <!-- Oda Sayısı -->
                    <TextBox Grid.Row="0" Grid.Column="2"
                             materialDesign:HintAssist.Hint="Oda Sayısı"
                             Text="{Binding CurrentFlat.RoomCount, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="10,0,0,20"/>

                    <!-- Alan -->
                    <TextBox Grid.Row="1" Grid.Column="0"
                             materialDesign:HintAssist.Hint="Alan (m²)"
                             Text="{Binding CurrentFlat.Area, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="0,0,10,20"/>

                    <!-- Aylık Kira -->
                    <TextBox Grid.Row="1" Grid.Column="1"
                             materialDesign:HintAssist.Hint="Aylık Kira"
                             Text="{Binding CurrentFlat.MonthlyRent, UpdateSourceTrigger=PropertyChanged}"
                             Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                             Margin="5,0,5,20"/>

                    <!-- Durum -->
                    <ComboBox Grid.Row="1" Grid.Column="2"
                              materialDesign:HintAssist.Hint="Durum"
                              SelectedItem="{Binding CurrentFlat.Status}"
                              Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                              Margin="10,0,0,20">
                        <ComboBoxItem Content="Boş"/>
                        <ComboBoxItem Content="Dolu"/>
                        <ComboBoxItem Content="Bakımda"/>
                    </ComboBox>

                    <!-- Aktif Durumu -->
                    <CheckBox Grid.Row="2" Grid.ColumnSpan="3"
                              Content="Aktif"
                              IsChecked="{Binding CurrentFlat.IsActive}"
                              Style="{DynamicResource MaterialDesignCheckBox}"
                              Margin="0,0,0,20"/>

                    <!-- Butonlar -->
                    <StackPanel Grid.Row="3" Grid.ColumnSpan="3" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Right">
                        <Button Content="İptal"
                                Command="{Binding CancelEditCommand}"
                                Style="{DynamicResource MaterialDesignFlatButton}"
                                Margin="0,0,10,0"/>
                        <Button Content="Kaydet"
                                Command="{Binding SaveFlatCommand}"
                                Style="{DynamicResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- Mesaj Alanları -->
        <StackPanel Grid.Row="3" Margin="0,10,0,0">
            <TextBlock Text="{Binding ErrorMessage}"
                       Foreground="Red"
                       Visibility="{Binding ErrorMessage, Converter={StaticResource BoolToVisibilityConverter}}"
                       Margin="0,5"/>
            <TextBlock Text="{Binding SuccessMessage}"
                       Foreground="Green"
                       Visibility="{Binding SuccessMessage, Converter={StaticResource BoolToVisibilityConverter}}"
                       Margin="0,5"/>
        </StackPanel>
    </Grid>
</UserControl>
