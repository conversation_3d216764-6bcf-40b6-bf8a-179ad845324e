<UserControl x:Class="ApartmanYonetimSistemi.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ApartmanYonetimSistemi.Views"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:helpers="clr-namespace:ApartmanYonetimSistemi.Helpers"
             mc:Ignorable="d" 
             d:DesignHeight="720" d:DesignWidth="1280">

    <UserControl.DataContext>
        <vm:DashboardViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="20,20,20,10" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Dashboard" FontSize="24" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding CurrentUser.FullName, StringFormat='Hoş geldiniz, {0}'}" 
                               FontSize="16" Foreground="Gray"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="Yenile" 
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,10,0"/>
                    <Button Content="Çıkış" 
                            Command="{Binding LogoutCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="#F44336"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="20,10,20,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Statistics Cards -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Total Apartments -->
                    <materialDesign:Card Grid.Column="0" Margin="5" Padding="20">
                        <StackPanel>
                            <materialDesign:PackIcon Kind="HomeCity" 
                                                     Width="40" 
                                                     Height="40" 
                                                     Foreground="#2196F3" 
                                                     HorizontalAlignment="Left"/>
                            <TextBlock Text="{Binding TotalApartments}" 
                                       FontSize="32" 
                                       FontWeight="Bold" 
                                       Margin="0,10,0,5"/>
                            <TextBlock Text="Toplam Daire" 
                                       FontSize="14" 
                                       Foreground="Gray"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Occupied Apartments -->
                    <materialDesign:Card Grid.Column="1" Margin="5" Padding="20">
                        <StackPanel>
                            <materialDesign:PackIcon Kind="AccountMultiple" 
                                                     Width="40" 
                                                     Height="40" 
                                                     Foreground="#4CAF50" 
                                                     HorizontalAlignment="Left"/>
                            <TextBlock Text="{Binding OccupiedApartments}" 
                                       FontSize="32" 
                                       FontWeight="Bold" 
                                       Margin="0,10,0,5"/>
                            <TextBlock Text="Dolu Daire" 
                                       FontSize="14" 
                                       Foreground="Gray"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Available Apartments -->
                    <materialDesign:Card Grid.Column="2" Margin="5" Padding="20">
                        <StackPanel>
                            <materialDesign:PackIcon Kind="HomeOutline" 
                                                     Width="40" 
                                                     Height="40" 
                                                     Foreground="#FF9800" 
                                                     HorizontalAlignment="Left"/>
                            <TextBlock Text="{Binding AvailableApartments}" 
                                       FontSize="32" 
                                       FontWeight="Bold" 
                                       Margin="0,10,0,5"/>
                            <TextBlock Text="Boş Daire" 
                                       FontSize="14" 
                                       Foreground="Gray"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Monthly Income -->
                    <materialDesign:Card Grid.Column="3" Margin="5" Padding="20">
                        <StackPanel>
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                     Width="40" 
                                                     Height="40" 
                                                     Foreground="#9C27B0" 
                                                     HorizontalAlignment="Left"/>
                            <TextBlock Text="{Binding TotalMonthlyIncome, StringFormat='₺{0:N0}'}" 
                                       FontSize="32" 
                                       FontWeight="Bold" 
                                       Margin="0,10,0,5"/>
                            <TextBlock Text="Aylık Gelir" 
                                       FontSize="14" 
                                       Foreground="Gray"/>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!-- Recent Data -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Recent Apartments -->
                    <materialDesign:Card Grid.Column="0" Margin="5,0,10,0" Padding="20">
                        <StackPanel>
                            <TextBlock Text="Son Daireler" 
                                       FontSize="18" 
                                       FontWeight="Bold" 
                                       Margin="0,0,0,15"/>
                            
                            <ItemsControl ItemsSource="{Binding RecentApartments}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,5" Padding="10" Background="#F5F5F5" CornerRadius="5">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding Number}" 
                                                               FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding Floor, StringFormat='{}{0}. Kat'}" 
                                                               FontSize="12" 
                                                               Foreground="Gray"/>
                                                </StackPanel>
                                                
                                                <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                                    <TextBlock Text="{Binding Status}" 
                                                               FontSize="12" 
                                                               Foreground="{Binding Status, Converter={StaticResource StatusToColorConverter}}"/>
                                                    <TextBlock Text="{Binding MonthlyRent, StringFormat='₺{0:N0}'}" 
                                                               FontSize="12" 
                                                               Foreground="Gray"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Recent Payments -->
                    <materialDesign:Card Grid.Column="1" Margin="10,0,5,0" Padding="20">
                        <StackPanel>
                            <TextBlock Text="Son Ödemeler" 
                                       FontSize="18" 
                                       FontWeight="Bold" 
                                       Margin="0,0,0,15"/>
                            
                            <ItemsControl ItemsSource="{Binding RecentPayments}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Margin="0,5" Padding="10" Background="#F5F5F5" CornerRadius="5">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding Type}" 
                                                               FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding DueDate, StringFormat='Vade: {0:dd.MM.yyyy}'}" 
                                                               FontSize="12" 
                                                               Foreground="Gray"/>
                                                </StackPanel>
                                                
                                                <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                                    <TextBlock Text="{Binding Amount, StringFormat='₺{0:N0}'}" 
                                                               FontWeight="Bold"/>
                                                    <TextBlock Text="{Binding Status}" 
                                                               FontSize="12" 
                                                               Foreground="{Binding Status, Converter={StaticResource PaymentStatusToColorConverter}}"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>
            </Grid>
        </ScrollViewer>
    </Grid>

    <UserControl.Resources>
        <helpers:StatusToColorConverter x:Key="StatusToColorConverter"/>
        <helpers:PaymentStatusToColorConverter x:Key="PaymentStatusToColorConverter"/>
    </UserControl.Resources>
</UserControl> 