using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace ApartmanYonetimSistemi;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            LogMessage("Uygulama başlatılıyor...");
            base.OnStartup(e);
            LogMessage("Base.OnStartup tamamlandı");
            
            SetupExceptionHandling();
            LogMessage("Exception handling kuruldu");

            // Ana pencereyi manuel ve güvenli bir şekilde başlat
            LogMessage("MainWindow oluşturuluyor...");
            var mainWindow = new MainWindow();
            LogMessage("MainWindow oluşturuldu, gösteriliyor...");
            mainWindow.Show();
            LogMessage("MainWindow gösterildi");
        }
        catch (Exception ex)
        {
            LogMessage($"OnStartup'da hata yakalandı: {ex.Message}");
            LogMessage($"StackTrace: {ex.StackTrace}");
            HandleStartupException(ex);
        }
    }

    private void SetupExceptionHandling()
    {
        // UI thread hataları
        DispatcherUnhandledException += (s, e) =>
        {
            LogException(e.Exception, "DispatcherUnhandledException");
            e.Handled = true;
        };

        // Non-UI thread hataları
        AppDomain.CurrentDomain.UnhandledException += (s, e) =>
        {
            LogException((Exception)e.ExceptionObject, "AppDomain.CurrentDomain.UnhandledException");
        };

        // Task hataları
        TaskScheduler.UnobservedTaskException += (s, e) =>
        {
            LogException(e.Exception, "TaskScheduler.UnobservedTaskException");
            e.SetObserved();
        };
    }

    private void HandleStartupException(Exception ex)
    {
        string errorMessage = $"Uygulama başlatılırken kritik bir hata oluştu:\n\n{ex.Message}\n\nDetaylar 'startup_error.log' dosyasına kaydedildi.";
        LogException(ex, "StartupException", "startup_error.log");
        MessageBox.Show(errorMessage, "Başlatma Hatası", MessageBoxButton.OK, MessageBoxImage.Error);
        Shutdown(1);
    }

    private void LogMessage(string message)
    {
        var logMessage = $"[{DateTime.Now:dd.MM.yyyy HH:mm:ss}] {message}";
        try
        {
            File.AppendAllText("debug.log", logMessage + Environment.NewLine);
        }
        catch (Exception ex)
        {
            // Loglama başarısız olursa konsola yazdır
            Console.WriteLine($"Loglama hatası: {ex.Message}");
            Console.WriteLine($"Orijinal mesaj: {message}");
        }
    }

    private void LogException(Exception ex, string source, string logFileName = "error.log")
    {
        var logMessage = $@"
===========================================================
Zaman: {DateTime.Now:dd.MM.yyyy HH:mm:ss}
Kaynak: {source}
Hata Türü: {ex.GetType().Name}
Hata Mesajı: {ex.Message}
StackTrace:
{ex.StackTrace}
===========================================================";

        try
        {
            File.AppendAllText(logFileName, logMessage + Environment.NewLine);
        }
        catch (Exception logEx)
        {
            // Loglama bile başarısız olursa...
            MessageBox.Show($"Kritik hata loglanamadı: {logEx.Message}\n\nOrijinal Hata: {ex.Message}", "Loglama Hatası", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}

