using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Helpers;
using System.Collections.ObjectModel;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class FlatViewModel : BaseViewModel
    {
        private ObservableCollection<Flat> _flats = new();
        private Flat? _selectedFlat;
        private Flat _currentFlat = new();
        private bool _isEditing = false;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;

        public ObservableCollection<Flat> Flats
        {
            get => _flats;
            set => SetProperty(ref _flats, value);
        }

        public Flat? SelectedFlat
        {
            get => _selectedFlat;
            set
            {
                if (SetProperty(ref _selectedFlat, value) && value != null)
                {
                    CurrentFlat = value.Clone();
                    IsEditing = false;
                }
            }
        }

        public Flat CurrentFlat
        {
            get => _currentFlat;
            set => SetProperty(ref _currentFlat, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        // Commands
        public RelayCommand LoadFlatsCommand { get; }
        public RelayCommand NewFlatCommand { get; }
        public RelayCommand EditFlatCommand { get; }
        public RelayCommand SaveFlatCommand { get; }
        public RelayCommand DeleteFlatCommand { get; }
        public RelayCommand CancelEditCommand { get; }

        public FlatViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();

            LoadFlatsCommand = new RelayCommand(async _ => await LoadFlatsAsync());
            NewFlatCommand = new RelayCommand(_ => NewFlat());
            EditFlatCommand = new RelayCommand(_ => EditFlat(), _ => SelectedFlat != null);
            SaveFlatCommand = new RelayCommand(async _ => await SaveFlatAsync(), _ => CanSaveFlat());
            DeleteFlatCommand = new RelayCommand(async _ => await DeleteFlatAsync(), _ => SelectedFlat != null);
            CancelEditCommand = new RelayCommand(_ => CancelEdit());

            _ = LoadFlatsAsync();
        }

        private async Task LoadFlatsAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();
                Flats.Clear();

                var flats = await _firebaseService.GetFlatsBySiteAsync("1"); // Mock site ID
                foreach (var flat in flats)
                {
                    Flats.Add(flat);
                }
            }
            catch (Exception ex)
            {
                SetError($"Daireler yüklenirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void NewFlat()
        {
            CurrentFlat = new Flat { IsActive = true, Status = "Boş" };
            IsEditing = true;
            SelectedFlat = null;
            ClearMessages();
        }

        private void EditFlat()
        {
            if (SelectedFlat != null)
            {
                CurrentFlat = SelectedFlat.Clone();
                IsEditing = true;
                ClearMessages();
            }
        }

        private async Task SaveFlatAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                if (SelectedFlat == null)
                {
                    // New flat
                    string newId = await _firebaseService.AddFlatAsync(CurrentFlat);
                    CurrentFlat.Id = newId;
                    Flats.Insert(0, CurrentFlat.Clone());
                    SetSuccess("Daire başarıyla eklendi");
                }
                else
                {
                    // Update existing flat
                    await _firebaseService.UpdateFlatAsync(CurrentFlat);
                    var existingFlat = Flats.FirstOrDefault(f => f.Id == CurrentFlat.Id);
                    if (existingFlat != null)
                    {
                        existingFlat.FlatNo = CurrentFlat.FlatNo;
                        existingFlat.Floor = CurrentFlat.Floor;
                        existingFlat.RoomCount = CurrentFlat.RoomCount;
                        existingFlat.Area = CurrentFlat.Area;
                        existingFlat.MonthlyRent = CurrentFlat.MonthlyRent;
                        existingFlat.Status = CurrentFlat.Status;
                        existingFlat.IsActive = CurrentFlat.IsActive;
                    }
                    SetSuccess("Daire başarıyla güncellendi");
                }

                CancelEdit();
            }
            catch (Exception ex)
            {
                SetError($"Daire kaydedilirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteFlatAsync()
        {
            if (SelectedFlat == null) return;

            try
            {
                IsLoading = true;
                ClearMessages();

                await _firebaseService.DeleteFlatAsync(SelectedFlat.Id);
                Flats.Remove(SelectedFlat);
                SelectedFlat = null;
                SetSuccess("Daire başarıyla silindi");
            }
            catch (Exception ex)
            {
                SetError($"Daire silinirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            IsEditing = false;
            CurrentFlat = new Flat { IsActive = true, Status = "Boş" };
            ClearMessages();
        }

        private bool CanSaveFlat()
        {
            return !string.IsNullOrWhiteSpace(CurrentFlat.FlatNo) &&
                   CurrentFlat.Floor >= 0 &&
                   CurrentFlat.RoomCount > 0 &&
                   CurrentFlat.Area > 0 &&
                   !IsLoading;
        }
    }
}
