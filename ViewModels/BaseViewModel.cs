using ApartmanYonetimSistemi.Helpers;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class BaseViewModel : ObservableObject
    {
        private bool _isLoading;
        private string _errorMessage = string.Empty;
        private string _successMessage = string.Empty;

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);
        public bool HasSuccess => !string.IsNullOrEmpty(SuccessMessage);

        protected void ClearMessages()
        {
            ErrorMessage = string.Empty;
            SuccessMessage = string.Empty;
        }

        protected void SetError(string message)
        {
            ErrorMessage = message;
            SuccessMessage = string.Empty;
        }

        protected void SetSuccess(string message)
        {
            SuccessMessage = message;
            ErrorMessage = string.Empty;
        }
    }
}
