using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Helpers;
using System.Collections.ObjectModel;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class ApartmentViewModel : BaseViewModel
    {
        private ObservableCollection<Apartment> _apartments = new();
        private Apartment? _selectedApartment;
        private Apartment _currentApartment = new();
        private bool _isEditing = false;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;

        public ObservableCollection<Apartment> Apartments
        {
            get => _apartments;
            set => SetProperty(ref _apartments, value);
        }

        public Apartment? SelectedApartment
        {
            get => _selectedApartment;
            set
            {
                if (SetProperty(ref _selectedApartment, value) && value != null)
                {
                    CurrentApartment = value.Clone();
                    IsEditing = false;
                }
            }
        }

        public Apartment CurrentApartment
        {
            get => _currentApartment;
            set => SetProperty(ref _currentApartment, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        // Commands
        public RelayCommand LoadApartmentsCommand { get; }
        public RelayCommand NewApartmentCommand { get; }
        public RelayCommand EditApartmentCommand { get; }
        public RelayCommand SaveApartmentCommand { get; }
        public RelayCommand DeleteApartmentCommand { get; }
        public RelayCommand CancelEditCommand { get; }

        public ApartmentViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();

            LoadApartmentsCommand = new RelayCommand(async _ => await LoadApartmentsAsync());
            NewApartmentCommand = new RelayCommand(_ => NewApartment());
            EditApartmentCommand = new RelayCommand(_ => EditApartment(), _ => SelectedApartment != null);
            SaveApartmentCommand = new RelayCommand(async _ => await SaveApartmentAsync(), _ => CanSaveApartment());
            DeleteApartmentCommand = new RelayCommand(async _ => await DeleteApartmentAsync(), _ => SelectedApartment != null);
            CancelEditCommand = new RelayCommand(_ => CancelEdit());

            _ = LoadApartmentsAsync();
        }

        private async Task LoadApartmentsAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();
                Apartments.Clear();

                // Mock data for now
                var apartments = new List<Apartment>
                {
                    new Apartment { Id = "1", ApartmentName = "A Block", Address = "Test Address 1", TotalFlats = 20, IsActive = true },
                    new Apartment { Id = "2", ApartmentName = "B Block", Address = "Test Address 2", TotalFlats = 15, IsActive = true }
                };

                foreach (var apartment in apartments)
                {
                    Apartments.Add(apartment);
                }
            }
            catch (Exception ex)
            {
                SetError($"Apartmanlar yüklenirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void NewApartment()
        {
            CurrentApartment = new Apartment { IsActive = true };
            IsEditing = true;
            SelectedApartment = null;
            ClearMessages();
        }

        private void EditApartment()
        {
            if (SelectedApartment != null)
            {
                CurrentApartment = SelectedApartment.Clone();
                IsEditing = true;
                ClearMessages();
            }
        }

        private async Task SaveApartmentAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                if (SelectedApartment == null)
                {
                    // New apartment
                    string newId = await _firebaseService.AddApartmentAsync(CurrentApartment);
                    CurrentApartment.Id = newId;
                    Apartments.Insert(0, CurrentApartment.Clone());
                    SetSuccess("Apartman başarıyla eklendi");
                }
                else
                {
                    // Update existing apartment
                    await _firebaseService.UpdateApartmentAsync(CurrentApartment);
                    var existingApartment = Apartments.FirstOrDefault(a => a.Id == CurrentApartment.Id);
                    if (existingApartment != null)
                    {
                        existingApartment.ApartmentName = CurrentApartment.ApartmentName;
                        existingApartment.Address = CurrentApartment.Address;
                        existingApartment.TotalFlats = CurrentApartment.TotalFlats;
                        existingApartment.IsActive = CurrentApartment.IsActive;
                    }
                    SetSuccess("Apartman başarıyla güncellendi");
                }

                CancelEdit();
            }
            catch (Exception ex)
            {
                SetError($"Apartman kaydedilirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteApartmentAsync()
        {
            if (SelectedApartment == null) return;

            try
            {
                IsLoading = true;
                ClearMessages();

                await _firebaseService.DeleteApartmentAsync(SelectedApartment.Id);
                Apartments.Remove(SelectedApartment);
                SelectedApartment = null;
                SetSuccess("Apartman başarıyla silindi");
            }
            catch (Exception ex)
            {
                SetError($"Apartman silinirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            IsEditing = false;
            CurrentApartment = new Apartment { IsActive = true };
            ClearMessages();
        }

        private bool CanSaveApartment()
        {
            return !string.IsNullOrWhiteSpace(CurrentApartment.ApartmentName) &&
                   !string.IsNullOrWhiteSpace(CurrentApartment.Address) &&
                   CurrentApartment.TotalFlats > 0 &&
                   !IsLoading;
        }
    }
}
