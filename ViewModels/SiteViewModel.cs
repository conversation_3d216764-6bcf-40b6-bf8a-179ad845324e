using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Helpers;
using System.Collections.ObjectModel;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class SiteViewModel : BaseViewModel
    {
        private ObservableCollection<Site> _sites = new();
        private Site? _selectedSite;
        private Site _currentSite = new();
        private bool _isEditing = false;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;

        public ObservableCollection<Site> Sites
        {
            get => _sites;
            set => SetProperty(ref _sites, value);
        }

        public Site? SelectedSite
        {
            get => _selectedSite;
            set
            {
                if (SetProperty(ref _selectedSite, value) && value != null)
                {
                    CurrentSite = value.Clone();
                    IsEditing = false;
                }
            }
        }

        public Site CurrentSite
        {
            get => _currentSite;
            set => SetProperty(ref _currentSite, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        // Commands
        public RelayCommand LoadSitesCommand { get; }
        public RelayCommand NewSiteCommand { get; }
        public RelayCommand EditSiteCommand { get; }
        public RelayCommand SaveSiteCommand { get; }
        public RelayCommand DeleteSiteCommand { get; }
        public RelayCommand CancelEditCommand { get; }

        public SiteViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();

            LoadSitesCommand = new RelayCommand(async _ => await LoadSitesAsync());
            NewSiteCommand = new RelayCommand(_ => NewSite());
            EditSiteCommand = new RelayCommand(_ => EditSite(), _ => SelectedSite != null);
            SaveSiteCommand = new RelayCommand(async _ => await SaveSiteAsync(), _ => CanSaveSite());
            DeleteSiteCommand = new RelayCommand(async _ => await DeleteSiteAsync(), _ => SelectedSite != null);
            CancelEditCommand = new RelayCommand(_ => CancelEdit());

            _ = LoadSitesAsync();
        }

        private async Task LoadSitesAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();
                Sites.Clear();

                var sites = await _firebaseService.GetAllSitesAsync();
                foreach (var site in sites)
                {
                    Sites.Add(site);
                }
            }
            catch (Exception ex)
            {
                SetError($"Siteler yüklenirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void NewSite()
        {
            CurrentSite = new Site { IsActive = true };
            IsEditing = true;
            SelectedSite = null;
            ClearMessages();
        }

        private void EditSite()
        {
            if (SelectedSite != null)
            {
                CurrentSite = SelectedSite.Clone();
                IsEditing = true;
                ClearMessages();
            }
        }

        private async Task SaveSiteAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                if (SelectedSite == null)
                {
                    // New site
                    string newId = await _firebaseService.AddSiteAsync(CurrentSite);
                    CurrentSite.Id = newId;
                    Sites.Insert(0, CurrentSite.Clone());
                    SetSuccess("Site başarıyla eklendi");
                }
                else
                {
                    // Update existing site
                    await _firebaseService.UpdateSiteAsync(CurrentSite);
                    var existingSite = Sites.FirstOrDefault(s => s.Id == CurrentSite.Id);
                    if (existingSite != null)
                    {
                        existingSite.SiteName = CurrentSite.SiteName;
                        existingSite.Address = CurrentSite.Address;
                        existingSite.Phone = CurrentSite.Phone;
                        existingSite.IsActive = CurrentSite.IsActive;
                    }
                    SetSuccess("Site başarıyla güncellendi");
                }

                CancelEdit();
            }
            catch (Exception ex)
            {
                SetError($"Site kaydedilirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteSiteAsync()
        {
            if (SelectedSite == null) return;

            try
            {
                IsLoading = true;
                ClearMessages();

                await _firebaseService.DeleteSiteAsync(SelectedSite.Id);
                Sites.Remove(SelectedSite);
                SelectedSite = null;
                SetSuccess("Site başarıyla silindi");
            }
            catch (Exception ex)
            {
                SetError($"Site silinirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            IsEditing = false;
            CurrentSite = new Site { IsActive = true };
            ClearMessages();
        }

        private bool CanSaveSite()
        {
            return !string.IsNullOrWhiteSpace(CurrentSite.SiteName) &&
                   !string.IsNullOrWhiteSpace(CurrentSite.Address) &&
                   !IsLoading;
        }
    }
}
