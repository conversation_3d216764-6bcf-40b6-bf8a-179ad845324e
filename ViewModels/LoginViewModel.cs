using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Models;
using System.Windows;
using System.Windows.Input;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class LoginViewModel : ObservableObject
    {
        public event EventHandler<User>? LoginSuccessful;
        private string _email = string.Empty;
        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        private string _password = string.Empty;
        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value);
        }

        private bool _isLoading = false;
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        private string? _errorMessage;
        public string? ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        // Commands
        public ICommand LoginCommand { get; }
        public ICommand ShowRegisterWindowCommand { get; }
        public ICommand ShowForgotPasswordWindowCommand { get; }

        public LoginViewModel()
        {
            LoginCommand = new RelayCommand(ExecuteLogin, CanExecuteLogin);
            ShowRegisterWindowCommand = new RelayCommand(ExecuteShowRegisterWindow);
            ShowForgotPasswordWindowCommand = new RelayCommand(ExecuteShowForgotPasswordWindow);
        }

        private bool CanExecuteLogin(object? parameter)
        {
            return !string.IsNullOrWhiteSpace(Email) && !string.IsNullOrWhiteSpace(Password) && !IsLoading;
        }

        private async void ExecuteLogin(object? parameter)
        {
            try
            {
                IsLoading = true;
                ErrorMessage = null;

                // Simüle edilmiş login işlemi
                await Task.Delay(1000);

                // Basit doğrulama (gerçek uygulamada veritabanından kontrol edilir)
                if (Email.ToLower() == "<EMAIL>" && Password == "123456")
                {
                    // Test kullanıcısı oluştur
                    var user = new User
                    {
                        Id = "admin-1",
                        FullName = "Admin Kullanıcı",
                        Email = Email,
                        Role = UserRoles.Admin,
                        IsActive = true
                    };

                    // Başarılı giriş eventini tetikle
                    LoginSuccessful?.Invoke(this, user);
                }
                else
                {
                    ErrorMessage = "E-posta adresi veya şifre hatalı. Lütfen tekrar deneyin.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Giriş sırasında bir hata oluştu: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteShowRegisterWindow(object? obj)
        {
            try
            {
                var registerWindow = new Views.RegisterView();
                registerWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kayıt penceresi açılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteShowForgotPasswordWindow(object? obj)
        {
            try
            {
                var forgotPasswordWindow = new Views.ForgotPasswordView();
                forgotPasswordWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Şifre sıfırlama penceresi açılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
} 