using ApartmanYonetimSistemi.Helpers;
using System.Windows;
using System.Windows.Input;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class RegisterViewModel : ObservableObject
    {
        private string _fullName = string.Empty;
        public string FullName
        {
            get => _fullName;
            set => SetProperty(ref _fullName, value);
        }

        private string _email = string.Empty;
        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        private string _password = string.Empty;
        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value);
        }

        private string _confirmPassword = string.Empty;
        public string ConfirmPassword
        {
            get => _confirmPassword;
            set => SetProperty(ref _confirmPassword, value);
        }

        private string? _errorMessage;
        public string? ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public ICommand RegisterCommand { get; }

        public RegisterViewModel()
        {
            RegisterCommand = new RelayCommand(ExecuteRegister, CanExecuteRegister);
        }

        private bool CanExecuteRegister(object? obj)
        {
            // Tüm alanlar doluysa komut çalışabilir
            return !string.IsNullOrWhiteSpace(FullName) &&
                   !string.IsNullOrWhiteSpace(Email) &&
                   !string.IsNullOrWhiteSpace(Password) &&
                   !string.IsNullOrWhiteSpace(ConfirmPassword);
        }

        private async void ExecuteRegister(object? obj)
        {
            try
            {
                ErrorMessage = null; // Önceki hata mesajını temizle

                // Validasyonlar
                if (Password != ConfirmPassword)
                {
                    ErrorMessage = "Girilen şifreler eşleşmiyor.";
                    return;
                }

                if (Password.Length < 6)
                {
                    ErrorMessage = "Şifre en az 6 karakter olmalıdır.";
                    return;
                }

                if (!IsValidEmail(Email))
                {
                    ErrorMessage = "Geçerli bir e-posta adresi giriniz.";
                    return;
                }

                // Simüle edilmiş kayıt işlemi
                await Task.Delay(1000);

                // Başarılı kayıt
                MessageBox.Show($"'{FullName}' adlı kullanıcı başarıyla kaydedildi!\n\nE-posta: {Email}", 
                               "Kayıt Başarılı", 
                               MessageBoxButton.OK, 
                               MessageBoxImage.Information);
                
                // Pencereyi kapat
                if (obj is Window window)
                {
                    window.Close();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Kayıt sırasında bir hata oluştu: {ex.Message}";
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
} 