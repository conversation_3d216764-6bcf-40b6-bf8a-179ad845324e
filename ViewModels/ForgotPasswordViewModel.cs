using ApartmanYonetimSistemi.Helpers;
using System.Windows;
using System.Windows.Input;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class ForgotPasswordViewModel : ObservableObject
    {
        private string _email = string.Empty;
        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        private bool _isLoading = false;
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        private string? _successMessage;
        public string? SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        public ICommand SendResetLinkCommand { get; }

        public ForgotPasswordViewModel()
        {
            SendResetLinkCommand = new RelayCommand(ExecuteSendResetLink, CanExecuteSendResetLink);
        }

        private bool CanExecuteSendResetLink(object? obj)
        {
            return !string.IsNullOrWhiteSpace(Email) && !IsLoading;
        }

        private async void ExecuteSendResetLink(object? obj)
        {
            try
            {
                IsLoading = true;
                SuccessMessage = null;

                // E-posta validasyonu
                if (!IsValidEmail(Email))
                {
                    MessageBox.Show("Geçerli bir e-posta adresi giriniz.", "Hata", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Simüle edilmiş e-posta gönderme işlemi
                await Task.Delay(2000);

                // Başarılı mesajı
                SuccessMessage = $"Şifre sıfırlama bağlantısı '{Email}' adresine gönderildi.\n\nLütfen e-postanızı kontrol edin.";
                
                MessageBox.Show(SuccessMessage, "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // Pencereyi kapat
                if (obj is Window window)
                {
                    window.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Şifre sıfırlama bağlantısı gönderilirken hata oluştu: {ex.Message}", 
                               "Hata", 
                               MessageBoxButton.OK, 
                               MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
} 