using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Helpers;
using System.Collections.ObjectModel;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class PaymentViewModel : BaseViewModel
    {
        private ObservableCollection<Payment> _payments = new();
        private Payment? _selectedPayment;
        private Payment _currentPayment = new();
        private bool _isEditing = false;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;

        public ObservableCollection<Payment> Payments
        {
            get => _payments;
            set => SetProperty(ref _payments, value);
        }

        public Payment? SelectedPayment
        {
            get => _selectedPayment;
            set
            {
                if (SetProperty(ref _selectedPayment, value) && value != null)
                {
                    CurrentPayment = new Payment
                    {
                        Id = value.Id,
                        FlatId = value.FlatId,
                        TenantId = value.TenantId,
                        ApartmentId = value.ApartmentId,
                        SiteId = value.SiteId,
                        Amount = value.Amount,
                        Type = value.Type,
                        DueDate = value.DueDate,
                        PaidDate = value.PaidDate,
                        Status = value.Status,
                        Description = value.Description,
                        PaymentMethod = value.PaymentMethod,
                        ReceiptNumber = value.ReceiptNumber,
                        CreatedDate = value.CreatedDate,
                        CreatedBy = value.CreatedBy
                    };
                    IsEditing = false;
                }
            }
        }

        public Payment CurrentPayment
        {
            get => _currentPayment;
            set => SetProperty(ref _currentPayment, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        // Commands
        public RelayCommand LoadPaymentsCommand { get; }
        public RelayCommand NewPaymentCommand { get; }
        public RelayCommand EditPaymentCommand { get; }
        public RelayCommand SavePaymentCommand { get; }
        public RelayCommand DeletePaymentCommand { get; }
        public RelayCommand CancelEditCommand { get; }

        public PaymentViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();

            LoadPaymentsCommand = new RelayCommand(async _ => await LoadPaymentsAsync());
            NewPaymentCommand = new RelayCommand(_ => NewPayment());
            EditPaymentCommand = new RelayCommand(_ => EditPayment(), _ => SelectedPayment != null);
            SavePaymentCommand = new RelayCommand(async _ => await SavePaymentAsync(), _ => CanSavePayment());
            DeletePaymentCommand = new RelayCommand(async _ => await DeletePaymentAsync(), _ => SelectedPayment != null);
            CancelEditCommand = new RelayCommand(_ => CancelEdit());

            _ = LoadPaymentsAsync();
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();
                Payments.Clear();

                var payments = await _firebaseService.GetPaymentsBySiteAsync("1"); // Mock site ID
                foreach (var payment in payments)
                {
                    Payments.Add(payment);
                }
            }
            catch (Exception ex)
            {
                SetError($"Ödemeler yüklenirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void NewPayment()
        {
            CurrentPayment = new Payment 
            { 
                Status = PaymentStatus.Pending,
                DueDate = DateTime.Now.AddDays(30),
                CreatedDate = DateTime.Now
            };
            IsEditing = true;
            SelectedPayment = null;
            ClearMessages();
        }

        private void EditPayment()
        {
            if (SelectedPayment != null)
            {
                IsEditing = true;
                ClearMessages();
            }
        }

        private async Task SavePaymentAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                if (SelectedPayment == null)
                {
                    // New payment
                    string newId = await _firebaseService.AddPaymentAsync(CurrentPayment);
                    CurrentPayment.Id = newId;
                    Payments.Insert(0, new Payment
                    {
                        Id = CurrentPayment.Id,
                        FlatId = CurrentPayment.FlatId,
                        TenantId = CurrentPayment.TenantId,
                        ApartmentId = CurrentPayment.ApartmentId,
                        SiteId = CurrentPayment.SiteId,
                        Amount = CurrentPayment.Amount,
                        Type = CurrentPayment.Type,
                        DueDate = CurrentPayment.DueDate,
                        Status = CurrentPayment.Status,
                        Description = CurrentPayment.Description,
                        CreatedDate = CurrentPayment.CreatedDate,
                        CreatedBy = CurrentPayment.CreatedBy
                    });
                    SetSuccess("Ödeme başarıyla eklendi");
                }
                else
                {
                    // Update existing payment
                    await _firebaseService.UpdatePaymentAsync(CurrentPayment);
                    var existingPayment = Payments.FirstOrDefault(p => p.Id == CurrentPayment.Id);
                    if (existingPayment != null)
                    {
                        existingPayment.Amount = CurrentPayment.Amount;
                        existingPayment.Type = CurrentPayment.Type;
                        existingPayment.DueDate = CurrentPayment.DueDate;
                        existingPayment.Status = CurrentPayment.Status;
                        existingPayment.Description = CurrentPayment.Description;
                    }
                    SetSuccess("Ödeme başarıyla güncellendi");
                }

                CancelEdit();
            }
            catch (Exception ex)
            {
                SetError($"Ödeme kaydedilirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeletePaymentAsync()
        {
            if (SelectedPayment == null) return;

            try
            {
                IsLoading = true;
                ClearMessages();

                await _firebaseService.DeletePaymentAsync(SelectedPayment.Id);
                Payments.Remove(SelectedPayment);
                SelectedPayment = null;
                SetSuccess("Ödeme başarıyla silindi");
            }
            catch (Exception ex)
            {
                SetError($"Ödeme silinirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            IsEditing = false;
            CurrentPayment = new Payment 
            { 
                Status = PaymentStatus.Pending,
                DueDate = DateTime.Now.AddDays(30),
                CreatedDate = DateTime.Now
            };
            ClearMessages();
        }

        private bool CanSavePayment()
        {
            return CurrentPayment.Amount > 0 &&
                   !string.IsNullOrWhiteSpace(CurrentPayment.Type) &&
                   !IsLoading;
        }
    }
}
