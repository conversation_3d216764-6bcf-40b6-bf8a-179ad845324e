using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Helpers;
using System.Collections.ObjectModel;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class TenantViewModel : BaseViewModel
    {
        private ObservableCollection<Tenant> _tenants = new();
        private Tenant? _selectedTenant;
        private Tenant _currentTenant = new();
        private bool _isEditing = false;
        private readonly FirebaseService _firebaseService;
        private readonly AuthService _authService;

        public ObservableCollection<Tenant> Tenants
        {
            get => _tenants;
            set => SetProperty(ref _tenants, value);
        }

        public Tenant? SelectedTenant
        {
            get => _selectedTenant;
            set
            {
                if (SetProperty(ref _selectedTenant, value) && value != null)
                {
                    CurrentTenant = value.Clone();
                    IsEditing = false;
                }
            }
        }

        public Tenant CurrentTenant
        {
            get => _currentTenant;
            set => SetProperty(ref _currentTenant, value);
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        // Commands
        public RelayCommand LoadTenantsCommand { get; }
        public RelayCommand NewTenantCommand { get; }
        public RelayCommand EditTenantCommand { get; }
        public RelayCommand SaveTenantCommand { get; }
        public RelayCommand DeleteTenantCommand { get; }
        public RelayCommand CancelEditCommand { get; }

        public TenantViewModel()
        {
            _firebaseService = new FirebaseService();
            _authService = new AuthService();

            LoadTenantsCommand = new RelayCommand(async _ => await LoadTenantsAsync());
            NewTenantCommand = new RelayCommand(_ => NewTenant());
            EditTenantCommand = new RelayCommand(_ => EditTenant(), _ => SelectedTenant != null);
            SaveTenantCommand = new RelayCommand(async _ => await SaveTenantAsync(), _ => CanSaveTenant());
            DeleteTenantCommand = new RelayCommand(async _ => await DeleteTenantAsync(), _ => SelectedTenant != null);
            CancelEditCommand = new RelayCommand(_ => CancelEdit());

            _ = LoadTenantsAsync();
        }

        private async Task LoadTenantsAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();
                Tenants.Clear();

                var tenants = await _firebaseService.GetTenantsBySiteAsync("1"); // Mock site ID
                foreach (var tenant in tenants)
                {
                    Tenants.Add(tenant);
                }
            }
            catch (Exception ex)
            {
                SetError($"Kiracılar yüklenirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void NewTenant()
        {
            CurrentTenant = new Tenant { IsActive = true };
            IsEditing = true;
            SelectedTenant = null;
            ClearMessages();
        }

        private void EditTenant()
        {
            if (SelectedTenant != null)
            {
                CurrentTenant = SelectedTenant.Clone();
                IsEditing = true;
                ClearMessages();
            }
        }

        private async Task SaveTenantAsync()
        {
            try
            {
                IsLoading = true;
                ClearMessages();

                if (SelectedTenant == null)
                {
                    // New tenant
                    string newId = await _firebaseService.AddTenantAsync(CurrentTenant);
                    CurrentTenant.Id = newId;
                    Tenants.Insert(0, CurrentTenant.Clone());
                    SetSuccess("Kiracı başarıyla eklendi");
                }
                else
                {
                    // Update existing tenant
                    await _firebaseService.UpdateTenantAsync(CurrentTenant);
                    var existingTenant = Tenants.FirstOrDefault(t => t.Id == CurrentTenant.Id);
                    if (existingTenant != null)
                    {
                        existingTenant.Name = CurrentTenant.Name;
                        existingTenant.Surname = CurrentTenant.Surname;
                        existingTenant.Phone = CurrentTenant.Phone;
                        existingTenant.Email = CurrentTenant.Email;
                        existingTenant.FlatNo = CurrentTenant.FlatNo;
                        existingTenant.IsActive = CurrentTenant.IsActive;
                    }
                    SetSuccess("Kiracı başarıyla güncellendi");
                }

                CancelEdit();
            }
            catch (Exception ex)
            {
                SetError($"Kiracı kaydedilirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteTenantAsync()
        {
            if (SelectedTenant == null) return;

            try
            {
                IsLoading = true;
                ClearMessages();

                await _firebaseService.DeleteTenantAsync(SelectedTenant.Id);
                Tenants.Remove(SelectedTenant);
                SelectedTenant = null;
                SetSuccess("Kiracı başarıyla silindi");
            }
            catch (Exception ex)
            {
                SetError($"Kiracı silinirken hata oluştu: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            IsEditing = false;
            CurrentTenant = new Tenant { IsActive = true };
            ClearMessages();
        }

        private bool CanSaveTenant()
        {
            return !string.IsNullOrWhiteSpace(CurrentTenant.Name) &&
                   !string.IsNullOrWhiteSpace(CurrentTenant.Surname) &&
                   !string.IsNullOrWhiteSpace(CurrentTenant.Phone) &&
                   !string.IsNullOrWhiteSpace(CurrentTenant.Email) &&
                   !IsLoading;
        }
    }
}
