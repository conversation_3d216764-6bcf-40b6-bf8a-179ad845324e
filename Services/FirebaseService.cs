using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public class FirebaseService
    {
        // Mock implementation for now
        public async Task<List<Site>> GetAllSitesAsync()
        {
            await Task.Delay(100); // Simulate async operation
            return new List<Site>
            {
                new Site { Id = "1", SiteName = "Test Site 1", Address = "Test Address 1", Phone = "555-1234", IsActive = true },
                new Site { Id = "2", SiteName = "Test Site 2", Address = "Test Address 2", Phone = "555-5678", IsActive = true }
            };
        }

        public async Task<List<Site>> GetSitesByOwnerAsync(string ownerId)
        {
            await Task.Delay(100);
            return new List<Site>
            {
                new Site { Id = "1", SiteName = "My Site", Address = "My Address", Phone = "555-0000", OwnerId = ownerId, IsActive = true }
            };
        }

        public async Task<List<Apartment>> GetApartmentsBySiteAsync(string siteId)
        {
            await Task.Delay(100);
            return new List<Apartment>
            {
                new Apartment { Id = "1", ApartmentName = "A Block", SiteId = siteId, TotalFlats = 20, IsActive = true },
                new Apartment { Id = "2", ApartmentName = "B Block", SiteId = siteId, TotalFlats = 15, IsActive = true }
            };
        }

        public async Task<string> AddSiteAsync(Site site)
        {
            await Task.Delay(100);
            site.Id = Guid.NewGuid().ToString();
            return site.Id;
        }

        public async Task UpdateSiteAsync(Site site)
        {
            await Task.Delay(100);
            // Mock update
        }

        public async Task DeleteSiteAsync(string siteId)
        {
            await Task.Delay(100);
            // Mock delete
        }

        public async Task<string> AddApartmentAsync(Apartment apartment)
        {
            await Task.Delay(100);
            apartment.Id = Guid.NewGuid().ToString();
            return apartment.Id;
        }

        public async Task UpdateApartmentAsync(Apartment apartment)
        {
            await Task.Delay(100);
            // Mock update
        }

        public async Task DeleteApartmentAsync(string apartmentId)
        {
            await Task.Delay(100);
            // Mock delete
        }

        public async Task<List<Flat>> GetFlatsBySiteAsync(string siteId)
        {
            await Task.Delay(100);
            return new List<Flat>
            {
                new Flat { Id = "1", FlatNo = "A1", SiteId = siteId, RentAmount = 2500, IsActive = true },
                new Flat { Id = "2", FlatNo = "A2", SiteId = siteId, RentAmount = 2500, IsActive = true }
            };
        }

        public async Task<List<Flat>> GetFlatsByApartmentAsync(string apartmentId)
        {
            await Task.Delay(100);
            return new List<Flat>
            {
                new Flat { Id = "1", FlatNo = "A1", ApartmentId = apartmentId, RentAmount = 2500, IsActive = true }
            };
        }

        public async Task<string> AddFlatAsync(Flat flat)
        {
            await Task.Delay(100);
            flat.Id = Guid.NewGuid().ToString();
            return flat.Id;
        }

        public async Task UpdateFlatAsync(Flat flat)
        {
            await Task.Delay(100);
            // Mock update
        }

        public async Task DeleteFlatAsync(string flatId)
        {
            await Task.Delay(100);
            // Mock delete
        }

        public async Task<List<Tenant>> GetTenantsBySiteAsync(string siteId)
        {
            await Task.Delay(100);
            return new List<Tenant>
            {
                new Tenant { Id = "1", Name = "John", Surname = "Doe", Phone = "555-1234", Email = "<EMAIL>", IsActive = true }
            };
        }

        public async Task<string> AddTenantAsync(Tenant tenant)
        {
            await Task.Delay(100);
            tenant.Id = Guid.NewGuid().ToString();
            return tenant.Id;
        }

        public async Task UpdateTenantAsync(Tenant tenant)
        {
            await Task.Delay(100);
            // Mock update
        }

        public async Task DeleteTenantAsync(string tenantId)
        {
            await Task.Delay(100);
            // Mock delete
        }

        public async Task<List<Payment>> GetPaymentsBySiteAsync(string siteId)
        {
            await Task.Delay(100);
            return new List<Payment>
            {
                new Payment { Id = "1", SiteId = siteId, Amount = 2500, Type = "Rent", Status = PaymentStatus.Paid }
            };
        }

        public async Task<string> AddPaymentAsync(Payment payment)
        {
            await Task.Delay(100);
            payment.Id = Guid.NewGuid().ToString();
            return payment.Id;
        }

        public async Task UpdatePaymentAsync(Payment payment)
        {
            await Task.Delay(100);
            // Mock update
        }

        public async Task DeletePaymentAsync(string paymentId)
        {
            await Task.Delay(100);
            // Mock delete
        }

        public async Task MarkPaymentAsPaidAsync(string paymentId, string paymentMethod, string receiptNumber, string processedBy)
        {
            await Task.Delay(100);
            // Mock mark as paid
        }
    }
}
