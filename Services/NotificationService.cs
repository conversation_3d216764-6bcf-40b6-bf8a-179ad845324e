using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public class NotificationService
    {
        public async Task<NotificationSummary?> GetNotificationSummaryAsync(string siteId)
        {
            await Task.Delay(100);
            return new NotificationSummary
            {
                TotalNotifications = 5,
                UnreadCount = 3,
                OverduePayments = 2,
                MaintenanceRequests = 1
            };
        }

        public async Task SendNotificationAsync(string userId, string title, string message)
        {
            await Task.Delay(100);
            // Mock send notification
        }

        public async Task SendEmailAsync(string email, string subject, string body)
        {
            await Task.Delay(100);
            // Mock send email
        }

        public async Task SendSmsAsync(string phone, string message)
        {
            await Task.Delay(100);
            // Mock send SMS
        }
    }

    public class NotificationSummary
    {
        public int TotalNotifications { get; set; }
        public int UnreadCount { get; set; }
        public int OverduePayments { get; set; }
        public int MaintenanceRequests { get; set; }
    }
}
