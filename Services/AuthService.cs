using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public class AuthService
    {
        public User? CurrentUser { get; private set; }

        public async Task<User?> LoginAsync(string email, string password)
        {
            await Task.Delay(100); // Simulate async operation
            
            // Mock login
            if (email.ToLower() == "<EMAIL>" && password == "123456")
            {
                CurrentUser = new User
                {
                    Id = "admin-1",
                    FullName = "Admin Kullanıcı",
                    Email = email,
                    Role = UserRoles.Admin,
                    IsActive = true
                };
                return CurrentUser;
            }
            
            return null;
        }

        public async Task<bool> RegisterAsync(User user, string password)
        {
            await Task.Delay(100);
            // Mock registration
            return true;
        }

        public async Task<bool> ResetPasswordAsync(string email)
        {
            await Task.Delay(100);
            // Mock password reset
            return true;
        }

        public void Logout()
        {
            CurrentUser = null;
        }

        public bool IsLoggedIn => CurrentUser != null;
    }
}
